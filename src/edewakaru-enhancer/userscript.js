// ==UserScript==
// @name                 「絵でわかる日本語」閲覧体験強化
// @namespace            http://tampermonkey.net/
// @version              2025.07.09
// <AUTHOR>
// @description          「絵でわかる日本語」サイトの漢字の読み方を括弧書きからルビ表示に自動変換し、広告や不要な要素を非表示にすることで、快適な読書環境を提供します。
// @description:en       Enhances reading experience on "絵でわかる日本語" site by converting kanji readings from parentheses to ruby text, and hiding ads/unnecessary elements.
// @description:zh-CN    将「絵でわかる日本語」网站的汉字注音从括号转换为振假名显示，并隐藏广告和无关元素，提升阅读体验。
// @description:zh-TW    將「絵でわかる日本語」網站的汉字注音從括號轉換為振假名顯示，並隱藏廣告和無關元素，提升閱讀體驗。
// @license              GPL-3.0
// @icon                 https://livedoor.blogimg.jp/edewakaru/imgs/8/c/8cdb7924.png
// @match                https://www.edewakaru.com/*
// @grant                GM_addStyle
// @grant                GM_getValue
// @grant                GM_setValue
// @run-at               document-start
// @downloadURL          https://update.greasyfork.org/scripts/542097/%E3%80%8C%E7%B5%B5%E3%81%A7%E3%82%8F%E3%81%8B%E3%82%8B%E6%97%A5%E6%9C%AC%E8%AA%9E%E3%80%8D%E9%96%B2%E8%A6%A7%E4%BD%93%E9%A8%93%E5%BC%B7%E5%8C%96.user.js
// @updateURL            https://update.greasyfork.org/scripts/542097/%E3%80%8C%E7%B5%B5%E3%81%A7%E3%82%8F%E3%81%8B%E3%82%8B%E6%97%A5%E6%9C%AC%E8%AA%9E%E3%80%8D%E9%96%B2%E8%A6%A7%E4%BD%93%E9%A8%93%E5%BC%B7%E5%8C%96.meta.js
// ==/UserScript==

;(function () {
  ;('use strict')

  // ===================================================================================
  // C O N F I G U R A T I O N
  // 脚本核心配置区域
  // 此处集中存放所有需要便捷修改的数据，方便未来维护
  // ===================================================================================
  const RULES = {
    /**
     * @property {Array<string>} STANDARD_WORDS
     * @description 标准格式的复合词列表，格式为 '汉字（注音）'
     * 这是最常用的格式，用于处理无法被自动分割算法正确处理的词汇 (对应处理优先级 1)
     */
    STANDARD_WORDS: [
      '合（あ）',
      '青い色（あおいいろ）',
      '足が早い（あしがはやい）',
      '当たり前（あたりまえ）',
      '甘い物（あまいもの）',
      '雨の日（あめのひ）',
      '歩（ある）',
      '行き方（いきかた）',
      '行き渡る（いきわたる）',
      '５日間（いつかかん）',
      '以上（いじょう）',
      '１回（いっかい）',
      '１か月（いっかげつ）',
      '１か月間（いっかげつかん）',
      '１杯（いっぱい）',
      '１泊（いっぱく）',
      '駅（えき）',
      '落ち着（おちつ）',
      'お茶する（おちゃする）',
      '思い出す（おもいだす）',
      '折に触れて（おりにふれて）',
      '折も折（おりもおり）',
      '折を見て（おりをみて）',
      '折があれば（おりがあれば）',
      'お団子（おだんご）',
      '買い物（かいもの）',
      '貸し借り（かしかり）',
      '髪の毛（かみのけ）',
      '唐揚げ（からあげ）',
      '考え方（かんがえかた）',
      '感じ方（かんじかた）',
      '聞き手（ききて）',
      '気持ち（きもち）',
      '口の中（くちのなか）',
      '触り心地（さわりごこち）',
      '三分の一（さんぶんのいち）',
      '試験（しけん）',
      '使用（しよう）',
      '出張（しゅっちょう）',
      '出張 （しゅっちょう）',
      '昭和の日（しょうわのひ）',
      '住（す）',
      '座り心地（すわりごこち）',
      '経（た）',
      '貯（た）',
      '立ち読み（たちよみ）',
      '楽（たの）',
      '食べ物（たべもの）',
      '食べず嫌い（たべずぎらい）',
      '遅刻（ちこく）',
      '使い方（つかいかた）',
      '使い分け（つかいわけ）',
      '届け出（とどけで）',
      '夏休み中（なつやすみちゅう）',
      '悩み事（なやみごと）',
      '長い間（ながいあいだ）',
      '入学（にゅうがく）',
      '日記（にっき）',
      '盗み食い（ぬすみぐい）',
      '寝（ね）',
      '寝る前（ねるまえ）',
      '入（はい）',
      '吐き気（はきけ）',
      '話し手（はなして）',
      '腹が立つ（はらがたつ）',
      '一つ（ひとつ）',
      '独り言（ひとりごと）',
      '星の数（ほしのかず）',
      '星の数ほどある（ほしのかずほどある）',
      '星の数ほどいる（ほしのかずほどいる）',
      '待（ま）',
      '前（まえ）',
      '万引き（まんびき）',
      '窓の外（まどのそと）',
      '真っ暗（まっくら）',
      '味覚 （みかく）',
      '申し訳（もうしわけ）',
      '元カレ（もとかれ）',
      '有名（ゆうめい）',
      '夕ご飯（ゆうごはん）',
      '呼び方（よびかた）',
      '０点（れいてん）',
      '連続（れんぞく）',
      '暮（ぐ）',
      '残業（ざんぎょう）',
      '自身（じしん）',
      '大好き（だいすき）',
      '動作（どうさ）',
      '通（どお）',
      '文法（ぶんぽう）',
      'アタック（attack）',
      'm（メートル）',
      '３杯（さんばい）',
      '話し言葉（はなしことば）',
      '１人（ひとり）',
      '分（ぶん）',
      '早寝早起き（はやねはやおき）',
      '社会（しゃかい）',
    ],

    /**
     * @property {Array<Object>} FORCED_READING
     * @description 强制指定注音的词汇列表当标准分割无法处理，且汉字和注音的组合形式特殊时使用 (对应处理优先级 1)
     * 格式：{ pattern: '原始文本', reading: '期望的注音' }
     */
    FORCED_READING: [
      { pattern: '羽根を伸ばす（羽根を伸ばす）', reading: 'はねをのばす' },
      { pattern: '長蛇の列（長蛇の列）', reading: 'ちょうだのれつ' },
      { pattern: '付き合（つきあい）', reading: 'つきあ' },
    ],

    /**
     * @property {Array<Object>} FORCED_REPLACEMENT
     * @description 强制进行全文替换的规则列表用于处理最复杂的情况，直接提供最终的 HTML 代码 (对应处理优先级 1)
     * 格式：{ pattern: '原始文本', replacement: '替换后的 HTML' }
     */
    FORCED_REPLACEMENT: [
      { pattern: '目に余る②（めにあまる）', replacement: '<ruby>目<rt>め</rt></ruby>に<ruby>余<rt>あま</rt></ruby>る②' },
      { pattern: '言い方（いいかた）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>' },
      { pattern: '言い訳（いいわけ）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>訳<rt>わけ</rt></ruby>' },
      { pattern: '原因・理由（げんいん・りゆう）', replacement: '<ruby>原因<rt>げんいん</rt></ruby>・<ruby>理由<rt>りゆう</rt></ruby>' },
      { pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）', replacement: '<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える' },
      { pattern: '水の泡になる・水の泡となる（みずのあわになる）', replacement: '<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>になる・<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>となる' },
      { pattern: '意味で（いみ）', replacement: '<ruby>意味<rt>いみ</rt></ruby>で' },
      { pattern: '和製英語で（わせいえいご）', replacement: '<ruby>和製英語<rt>わせいえいご</rt></ruby>で' },
      { pattern: '財布を（さいふ）', replacement: '<ruby>財布<rt>さいふ</rt></ruby>を' },
    ],

    /**
     * @property {Array<Object>} HTML_REPLACEMENT_RULES
     * @description 在 HTML 层面上进行正则替换的规则用于处理跨越 HTML 标签的注音模式 (对应处理优先级 0)
     * 格式：{ pattern: /正则表达式/g, replacement: '替换字符串' }
     */
    HTML_REPLACEMENT_RULES: [
      { pattern: /一瞬（いっしゅん<br>）/g, replacement: '<ruby>一瞬<rt>いっしゅん</rt></ruby>' },
      { pattern: /<b><span style="font-size: 125%;">居<\/span><\/b>（い）/g, replacement: '<b><ruby>居<rt>い</rt></ruby></b>' },
      { pattern: /<b style="font-size: large;">留守<\/b>（るす）/g, replacement: '<b><ruby>留守<rt>るす</rt></ruby></b>' },
      { pattern: /<span style="color: rgb\(255, 0, 0\);"><b>次第<\/b><\/span>（しだい）/g, replacement: '<span style="color: rgb(255, 0, 0);"><b><ruby>次第<rt>しだい</rt></ruby></b></span>' },
    ],

    /**
     * @property {Set<string>} ALWAYS_EXCLUDE
     * @description 始终不进行注音转换的特定模式 (用于优先级 4 的检查)
     */
    ALWAYS_EXCLUDE: new Set(['挙句（に）', '道草（を）', '以上（は）', '人称（私）', '人称（あなた）', '矢先（に）']),

    /**
     * @property {Set<string>} RUBY_EXCLUDE_PARTICLES
     * @description 当括号内的内容是这些助词，且括号前的文本是纯假名时，不进行转换 (用于优先级 4 的检查)
     * 例如，避免将 `あげく（に）` 错误地转换为 `<ruby>あげく<rt>に</rt></ruby>`
     */
    RUBY_EXCLUDE_PARTICLES: new Set(['に', 'は', 'を', 'が', 'の', 'と', 'で', 'から', 'まで', 'へ', 'も', 'や', 'ね', 'よ', 'さ']),
  }

  /**
   * @module PageOptimizer
   * @description 负责页面布局优化、样式注入和无关元素清理
   * 此模块通过注入 CSS 来重置页面布局，并移除广告、页眉、页脚等不需要的全局元素，
   * 同时也负责清理文章内容区域和调整侧边栏，为用户提供一个干净、专注的阅读环境
   */
  const PageOptimizer = {
    // 内部配置
    _config: {
      GLOBAL_REMOVE_SELECTORS: ['header#blog-header', 'footer#blog-footer', '.ldb_menu', '.article-social-btn', '.adsbygoogle', 'a[href*="blogmura.com"]', 'a[href*="with2.net"]'],
      INITIAL_PAGE_STYLES: `
        #container { width: 100%; } @media (min-width: 960px) { #container { max-width: 960px; } } @media (min-width: 1040px) { #container { max-width: 1040px; } }
        #content { display: flex; position: relative; padding: 50px 0 !important; } #main { flex: 1; float: none !important; width: 100% !important; }
        aside#sidebar { visibility: hidden; float: none !important; width: 350px !important; flex: 0 0 350px; }
        .plugin-categorize { position: fixed; height: 85vh; display: flex; flex-direction: column; padding: 0 !important; width: 350px !important; } .plugin-categorize .side { flex: 1; overflow-y: auto; max-height: unset; } .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) { margin-top: 5px; margin-bottom: 0; }
        .article { padding: 0 0 20px 0 !important; margin-bottom: 30px !important; } .article-body { padding: 0 !important; } .article-pager { margin-bottom: 0 !important; }
        .article-body-inner { line-height: 2; opacity: 0; transition: opacity 0.3s; } .article-body-inner img.pict { margin: 0 !important; width: 80% !important; display: block; } .article-body-inner strike { color: orange; } .article-body-inner iframe { margin: 4px 0 !important; }
        .to-pagetop { position: fixed; bottom: 1.2rem; right: 220px; z-index: 1000; }
        rt, iframe, .pager { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }
        header#blog-header, footer#blog-footer, .ldb_menu, .article-social-btn, .adsbygoogle, #ldblog_related_articles_01d4ecf1, #ad2 { display: none !important; }
        .article-body-inner:after, .article-meta:after, #container:after, #content:after, article:after, section:after, .cf:after { content: none !important; display: none !important; height: auto !important; visibility: visible !important; }
      `,
    },
    /**
     * 初始化模块，注入样式并移除全局元素
     */
    init() {
      GM_addStyle(this._config.INITIAL_PAGE_STYLES)
      this._removeGlobalElements()
    },
    /**
     * 批量移除页面中不需要的全局元素
     */
    _removeGlobalElements() {
      document.querySelectorAll(this._config.GLOBAL_REMOVE_SELECTORS.join(',')).forEach((el) => el.remove())
    },
    /**
     * 清理单个文章容器中的广告、脚本等，并使其可见
     * @param {HTMLElement} container - 文章容器元素
     */
    cleanupArticleBody(container) {
      const elementsToRemove = [...container.querySelectorAll('a[href*="blogmura.com"], a[href*="with2.net"], script')]
      const adDiv = container.querySelector('#ad2')
      if (adDiv) {
        let next = adDiv.nextElementSibling
        while (next) {
          elementsToRemove.push(next)
          next = next.nextElementSibling
        }
        elementsToRemove.push(adDiv)
      }
      elementsToRemove.forEach((el) => el.remove())
      this._trimContainerBreaks(container)
      // 淡入显示已处理完成的内容
      container.style.opacity = 1
    },
    /**
     * 移除容器开头和结尾的空白文本节点、<br>等
     * @param {HTMLElement} container - 需要清理的容器元素
     */
    _trimContainerBreaks(container) {
      // 定义一个函数来判断节点是否为空白
      const isWhitespaceNode = (node) => !node || (node.nodeType === 3 && /^\s*$/.test(node.textContent)) || (node.nodeType === 1 && (node.tagName === 'BR' || (node.tagName === 'SPAN' && node.innerHTML === ' ')))
      // 从开头循环移除空白节点
      while (isWhitespaceNode(container.firstChild)) container.removeChild(container.firstChild)
      // 从结尾循环移除空白节点
      while (isWhitespaceNode(container.lastChild)) container.removeChild(container.lastChild)
    },
    /**
     * 优化侧边栏，只保留分类并使其可见
     */
    finalizeLayout() {
      const sidebar = document.querySelector('aside#sidebar')
      if (!sidebar) return
      const category = sidebar.querySelector('.plugin-categorize')
      // 清空侧边栏现有内容
      sidebar.textContent = ''
      if (category) {
        // 只将分类插件加回去
        sidebar.appendChild(category)
        // 使侧边栏可见
        sidebar.style.visibility = 'visible'
      }
    },
  }

  /**
   * @module ImageProcessor
   * @description 专门处理博客图片链接，将其转换为直接的图片元素
   * 此模块查找页面中指向 livedoor 图床的链接，并将它们替换为高质量的 `<img>` 标签，
   * 从而优化图片加载和显示体验
   */
  const ImageProcessor = {
    // 内部配置
    _config: {
      // 匹配 livedoor 缩略图链接的正则表达式
      IMG_SRC_REGEX: /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i,
    },
    /**
     * 处理指定容器内的所有图片链接
     * @param {HTMLElement} container - 包含图片链接的容器元素
     */
    process(container) {
      container.querySelectorAll('a[href*="livedoor.blogimg.jp"]').forEach((link) => {
        const img = link.querySelector('img.pict')
        if (!img) return
        // 创建新的图片元素
        const newImg = document.createElement('img')
        // 移除 '-s' 后缀以获取原图
        newImg.src = img.src.replace(this._config.IMG_SRC_REGEX, '$1$2')
        // 继承原有属性
        newImg.alt = (img.alt || '').replace(/blog/gi, '')
        Object.assign(newImg, { className: img.className, width: img.width, height: img.height })
        // 用新的图片元素替换整个链接
        link.replaceWith(newImg)
      })
    },
  }

  /**
   * @module IframeLazyLoader
   * @description 负责 iframe 的加载策略管理。支持“延迟加载”和“点击加载”两种模式
   */
  const IframeLazyLoader = {
    _config: {
      IFRAME_SELECTOR: 'iframe[src*="richlink.blogsys.jp"]',
      ROOT_MARGIN: '200px 0px',
      LAZY_LOAD_CLASS: 'lazy-load-iframe-placeholder',
      CLICK_LOAD_CLASS: 'click-to-load-iframe-placeholder',
      STYLES: `
        .lazy-load-iframe-placeholder { display: inline-block; vertical-align: top; background-color: #fafafa; box-sizing: border-box; margin: 4px 0; background-image: url('data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%20preserveAspectRatio%3D%22xMidYMid%22%20width%3D%2240%22%20height%3D%2240%22%20style%3D%22shape-rendering%3A%20auto%3B%20background%3A%20none%3B%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2250%22%20r%3D%2232%22%20stroke-width%3D%228%22%20stroke%3D%22%23cccccc%22%20stroke-dasharray%3D%2250.26548245743669%2050.26548245743669%22%20fill%3D%22none%22%20stroke-linecap%3D%22round%22%3E%3CanimateTransform%20attributeName%3D%22transform%22%20type%3D%22rotate%22%20repeatCount%3D%22indefinite%22%20dur%3D%221s%22%20keyTimes%3D%220%3B1%22%20values%3D%220%2050%2050%3B360%2050%2050%22%3E%3C%2FanimateTransform%3E%3C%2Fcircle%3E%3C%2Fsvg%3E'); background-repeat: no-repeat; background-position: center; }
        .click-to-load-iframe-placeholder { opacity: 0.8; display: inline-grid; place-items: center; vertical-align: top; border: 1px solid #e5e5e5; color: #aaa; background-color: #fafafa; font-weight: bold; font-size: 16px; cursor: pointer; transition: background-color 0.2s, color 0.2s, border-color 0.2s; box-sizing: border-box; margin: 4px 0; }
        .click-to-load-iframe-placeholder:hover { opacity: 0.8; border-color: #3B82F6; color: #3B82F6; background-color: #f4f8ff; }
        @media screen and (max-width: 870px) { .lazy-load-iframe-placeholder, .click-to-load-iframe-placeholder { max-width: 350px !important; height: 105px !important; margin-bottom:19px; } }
        @media screen and (min-width: 871px) { .lazy-load-iframe-placeholder, .click-to-load-iframe-placeholder { max-width: 580px !important; height: 120px !important; } }
      `,
    },
    _observer: null,
    _isSupported: true,

    /**
     * 初始化模块，注入样式并根据用户设置准备相应的加载器
     */
    init() {
      GM_addStyle(this._config.STYLES)

      if (!('IntersectionObserver' in window)) {
        this._isSupported = false
        console.warn('IntersectionObserver not supported.')
        return
      }

      // 只在默认的“延迟加载”模式下才创建 Observer
      if (SettingsPanel.isLazyLoadEnabled()) {
        this._observer = new IntersectionObserver(
          (entries, observer) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                const iframe = entry.target
                const src = iframe.dataset.src
                const originalStyle = iframe.dataset.originalStyle

                if (src) {
                  iframe.onload = () => {
                    iframe.classList.remove(this._config.LAZY_LOAD_CLASS)
                    iframe.onload = null
                  }
                  iframe.setAttribute('style', originalStyle)
                  iframe.src = src
                  iframe.removeAttribute('data-src')
                  iframe.removeAttribute('data-original-style')
                }
                observer.unobserve(iframe)
              }
            })
          },
          { rootMargin: this._config.ROOT_MARGIN },
        )
      }
    },

    /**
     * 主处理函数，根据设置分发到不同的处理逻辑
     * @param {HTMLElement} container - 文章容器
     */
    processContainer(container) {
      const iframes = container.querySelectorAll(this._config.IFRAME_SELECTOR)
      if (iframes.length === 0) return // 如果没有 iframe 提前返回
      SettingsPanel.isLazyLoadEnabled() ? this._processForLazyLoad(iframes) : this._processForClickToLoad(iframes)
    },

    /**
     * 策略一：为全部的 iframe 设置延迟加载
     * @param {HTMLElement} iframes
     */
    _processForLazyLoad(iframes) {
      if (!this._isSupported || !this._observer) return

      iframes.forEach((iframe) => {
        const originalSrc = iframe.getAttribute('src')
        if (originalSrc && originalSrc !== 'about:blank' && !iframe.hasAttribute('data-src')) {
          const originalStyle = iframe.getAttribute('style') || ''
          iframe.dataset.originalStyle = originalStyle
          iframe.dataset.src = originalSrc
          iframe.src = 'about:blank'
          iframe.classList.add(this._config.LAZY_LOAD_CLASS)
          this._observer.observe(iframe)
        }
      })
    },

    /**
     * 策略二：将全部 iframe 替换为可点击的占位符
     * @param {HTMLElement} iframes
     */
    _processForClickToLoad(iframes) {
      iframes.forEach((iframe) => {
        // 通过检查父元素是否已经是占位符来防止重复处理
        if (iframe.parentElement.classList.contains(this._config.CLICK_LOAD_CLASS)) return

        const originalSrc = iframe.src
        const originalStyle = iframe.getAttribute('style') || ''

        const placeholder = document.createElement('div')
        placeholder.className = this._config.CLICK_LOAD_CLASS
        placeholder.textContent = '▶ 関連記事を読み込む'
        placeholder.setAttribute('style', originalStyle)

        placeholder.addEventListener(
          'click',
          () => {
            const newIframe = document.createElement('iframe')
            newIframe.src = originalSrc
            newIframe.setAttribute('style', originalStyle)
            newIframe.setAttribute('frameborder', '0')
            newIframe.setAttribute('scrolling', 'no')
            placeholder.replaceWith(newIframe)
          },
          { once: true },
        )
        iframe.replaceWith(placeholder)
      })
    },
  }

  /**
   * @module RubyConverter
   * @description 封装所有关于注音转换的逻辑，包括词库、规则和 DOM 处理
   * 这是脚本的核心功能模块，它遵循一个明确的优先级顺序来转换文本：
   * 优先级 0: HTML 级别强制替换 (HTML_REPLACEMENT_RULES)
   * 优先级 1: 手动指定的特例词汇 (STANDARD_WORDS, FORCED_READING, FORCED_REPLACEMENT)
   * 优先级 2: bracket 动态学习的词汇 (从【...】或「...」中提取)
   * 优先级 3: katakana片假名(英文)模式
   * 优先级 4: ruby 通用汉字(注音)模式，并应用各种排除规则
   */
  const RubyConverter = {
    _config: null, // 外部配置的引用
    // 内部正则表达式
    _regex: {
      bracket: /[【「](?:.*?)([^【】「」（）・、\s～〜]+)（([^（）]*)）([^【】「」（）]*)[】」]/g,
      katakana: /([ァ-ンー]+)[（(]([\w\s+]+)[）)]/g,
      ruby: /([一-龯々]+)\s*（([^（）]*)）/g,

      kanaOnly: /^[\u3040-\u309F]+$/,
      nonKana: /[^\u3040-\u309F]/,
      isKanaChar: /^[\u3040-\u309F]$/,
      hasInvalidChars: /[^一-龯々\u3040-\u309F\u30A0-\u30FF]/,
    },
    // 预处理后的词库和动态学习的词汇
    _processedWords: { patternResults: new Map(), globalRegex: null },
    _dynamicWords: new Set(),
    /**
     * 初始化模块，接收外部配置并预处理词库以提高性能
     * @param {object} config - 包含所有规则和词汇列表的外部配置对象
     */
    init(config) {
      this._config = config
      // 将所有特例词合并进行预处理
      this._preprocessWords(config)
    },
    /**
     * 处理指定容器，按优先级顺序应用所有转换规则
     * @param {HTMLElement} container - 需要处理的根元素
     */
    processContainer(container) {
      this._applyHtmlReplacements(container) // 优先级 0
      this._findAndRegisterCompounds(container) // 优先级 2 (学习过程)
      this._processRubyInNodes(container) // 应用 优先级 1, 3, 4
    },
    // 辅助函数：转义正则特殊字符
    _escapeRegExp: (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
    /**
     * 解析不同格式的特例词条
     * @param {string|object} entry - 原始词条
     * @returns {object|null} - 解析后的对象或 null
     */
    _parseCompoundEntry(entry) {
      if (typeof entry === 'string') {
        const match = entry.match(/(.*?)（(.*?)）/)
        if (match) return { pattern: entry, kanji: match[1].trim(), reading: match[2] }
      } else if (entry.reading) {
        return { pattern: entry.pattern, kanji: entry.pattern.replace(/（.*?）/, ''), reading: entry.reading }
      } else if (entry.replacement) {
        return entry
      }
      return null
    },
    /**
     * 预处理所有特例词，生成替换结果并构建全局正则表达式
     * @param {object} config - 完整的 RULES 对象
     */
    _preprocessWords(config) {
      const allPatterns = []
      // 1. 处理标准词汇: '汉字（注音）'
      config.STANDARD_WORDS.forEach((entry) => {
        const match = entry.match(/(.*?)（(.*?)）/)
        if (!match) return
        const pattern = entry
        const kanji = match[1].trim()
        const reading = match[2]
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, this._segmentCompoundWord(kanji, reading))
      })
      // 2. 处理强制注音: { pattern, reading }
      config.FORCED_READING.forEach((entry) => {
        const { pattern, reading } = entry
        const kanji = pattern.replace(/（.*?）/, '')
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, this._segmentCompoundWord(kanji, reading))
      })
      // 3. 处理强制替换: { pattern, replacement }
      config.FORCED_REPLACEMENT.forEach((entry) => {
        const { pattern, replacement } = entry
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, replacement)
      })

      // 最后，根据收集到的所有模式构建全局正则表达式
      this._rebuildGlobalRegex(allPatterns)
    },
    /**
     * 根据模式列表重新构建全局正则表达式
     * @param {Array<string>} patterns - 正则表达式模式字符串数组
     */
    _rebuildGlobalRegex(patterns) {
      this._processedWords.globalRegex = patterns.length > 0 ? new RegExp(`(${patterns.join('|')})`, 'g') : null
    },
    /**
     * 智能分割复合词的汉字和读音，生成 Ruby 标签
     * @param {string} kanji - 汉字部分 (可能包含假名)
     * @param {string} reading - 读音部分
     * @returns {string} - 生成的 HTML Ruby 字符串
     */
    _segmentCompoundWord(kanji, reading) {
      let result = '',
        kanjiIndex = 0,
        readingIndex = 0
      while (kanjiIndex < kanji.length) {
        if (this._regex.isKanaChar.test(kanji[kanjiIndex])) {
          // 如果是假名，直接附加
          result += kanji[kanjiIndex]
          readingIndex = reading.indexOf(kanji[kanjiIndex], readingIndex) + 1
          kanjiIndex++
        } else {
          // 处理连续的汉字块
          let kanjiPart = ''
          while (kanjiIndex < kanji.length && !this._regex.isKanaChar.test(kanji[kanjiIndex])) {
            kanjiPart += kanji[kanjiIndex++]
          }
          const nextKanaIndex = kanjiIndex < kanji.length ? reading.indexOf(kanji[kanjiIndex], readingIndex) : reading.length
          result += `<ruby>${kanjiPart}<rt>${reading.substring(readingIndex, nextKanaIndex)}</rt></ruby>`
          readingIndex = nextKanaIndex
        }
      }
      return result
    },
    /**
     * 对文本内容应用所有注音转换规则
     * @param {string} text - 原始文本节点内容
     * @returns {string} - 转换后的 HTML 字符串
     */
    _processTextContent(text) {
      if (!text.includes('（') && !text.includes('(')) return text

      // 优先级 1 & 2: 应用预处理的特例词和动态学习的词汇
      if (this._processedWords.globalRegex) {
        text = text.replace(this._processedWords.globalRegex, (match) => this._processedWords.patternResults.get(match) || match)
      }

      // 优先级 3: 处理片假名(英文)模式
      text = text.replace(this._regex.katakana, (_, katakana, romaji) => `<ruby>${katakana}<rt>${romaji}</rt></ruby>`)

      // 优先级 4: 处理通用汉字(注音)模式，并应用排除规则
      return text.replace(this._regex.ruby, (match, kanji, reading) => {
        const fullMatch = `${kanji}（${reading}）`
        // 执行各项检查
        if (this._config.ALWAYS_EXCLUDE.has(fullMatch) || (this._config.RUBY_EXCLUDE_PARTICLES.has(reading) && this._regex.kanaOnly.test(kanji)) || this._regex.nonKana.test(reading)) {
          return match // 不满足条件，返回原文
        }
        return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : match
      })
    },
    /**
     * 应用 HTML 级别的替换规则 (优先级 0)
     * @param {HTMLElement} element - 目标元素
     */
    _applyHtmlReplacements(element) {
      let html = element.innerHTML
      this._config.HTML_REPLACEMENT_RULES.forEach((rule) => {
        html = html.replace(rule.pattern, rule.replacement)
      })
      if (html !== element.innerHTML) element.innerHTML = html
    },
    /**
     * 扫描并学习新的复合词条 (优先级 2)
     * @param {HTMLElement} element - 需要扫描的元素
     */
    _findAndRegisterCompounds(element) {
      let match,
        newPatterns = []
      const html = element.innerHTML
      // 使用 bracket 正则匹配【...】或「...」中的词条
      while ((match = this._regex.bracket.exec(html)) !== null) {
        // 直接从捕获组获取数据
        const kanjiPart = match[1] // 捕获组 1: 汉字部分
        const readingPart = match[2] // 捕获组 2: 读音部分
        const suffixPart = match[3] // 捕获组 3: 后缀部分
        // 进行有效性检查
        if (
          match[0].includes('＋') ||
          match[0].includes('+') ||
          this._regex.nonKana.test(readingPart) || // 规则 1：读音部分必须是纯假名
          this._regex.hasInvalidChars.test(kanjiPart) // 规则 2：汉字部分不能含有特殊符号
        ) {
          continue
        }
        // 组合成完整的模式，用作 Map 的键
        const fullPattern = `${kanjiPart}（${readingPart}）${suffixPart}`
        // 如果是新词，则学习它
        if (!this._dynamicWords.has(fullPattern)) {
          this._dynamicWords.add(fullPattern)
          // 直接使用捕获到的部分生成 ruby HTML，并附加后缀
          const rubyHtml = this._segmentCompoundWord(kanjiPart, readingPart) + suffixPart
          // 将转换结果存入缓存，并准备更新全局正则
          this._processedWords.patternResults.set(fullPattern, rubyHtml)
          newPatterns.push(this._escapeRegExp(fullPattern))
        }
      }

      // 如果有新学习的词汇，则更新全局正则表达式
      if (newPatterns.length > 0) {
        const existing = this._processedWords.globalRegex ? this._processedWords.globalRegex.source.slice(1, -2).split('|') : []
        this._rebuildGlobalRegex([...existing, ...newPatterns])
      }
    },
    /**
     * 遍历 DOM，将文本节点批量转换为包含 Ruby 标签的 HTML
     * @param {HTMLElement} root - 根元素
     */
    _processRubyInNodes(root) {
      // 使用 TreeWalker 高效遍历所有文本节点
      const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
        acceptNode: (n) => (n.parentNode.nodeName !== 'SCRIPT' && n.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
      })
      const nodesToProcess = []
      let node
      // 收集所有需要修改的文本节点
      while ((node = walker.nextNode())) {
        const newContent = this._processTextContent(node.nodeValue)
        if (newContent !== node.nodeValue) nodesToProcess.push({ node, newContent })
      }
      // 从后往前批量替换，避免 DOM 位置错乱
      for (let i = nodesToProcess.length - 1; i >= 0; i--) {
        const { node, newContent } = nodesToProcess[i]
        // 使用 Range API 高效地将 HTML 字符串替换文本节点
        node.parentNode.replaceChild(document.createRange().createContextualFragment(newContent), node)
      }
    },
  }

  /**
   * @module SettingsPanel
   * @description 管理设置面板的创建、事件处理和状态持久化
   * 此模块负责在页面上创建一个浮动设置面板，允许用户动态地开关脚本功能和调整振假名 (Furigana) 的可见性
   * 所有设置都会通过 GM_setValue/GM_getValue 进行持久化保存
   */
  const SettingsPanel = {
    // 内部配置
    _config: {
      // 增加新的 KEY 用于存储 iframe 加载策略
      KEYS: { SCRIPT_ENABLED: 'ruby_converter_enabled', FURIGANA_VISIBLE: 'furigana_visible', IFRAME_LAZY_LOAD: 'iframe_lazy_load' },
      STYLES: `
        #settings-panel { position: fixed; bottom: 1.5rem; right: 1.5rem; z-index: 9999; display: flex; flex-direction: column; gap: 0.5rem; padding: 1rem; background: white; border-radius: 4px; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); width: 140px; opacity: 0.8; user-select: none; } .settings-title { font-size: 0.875rem; font-weight: 600; color: #1F2937; margin: 0 0 0.375rem 0; text-align: center; border-bottom: 1px solid #E5E7EB; padding-bottom: 0.375rem; }
        .setting-item { display: flex; align-items: center; justify-content: space-between; gap: 0.5rem; } .setting-label { font-size: 0.8125rem; font-weight: 500; color: #4B5563; cursor: pointer; flex: 1; line-height: 1.2; }
        .toggle-switch { position: relative; display: inline-block; width: 2.5rem; height: 1.25rem; flex-shrink: 0; } .toggle-switch input { opacity: 0; width: 0; height: 0; }
        .toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #E5E7EB; transition: all 0.2s ease-in-out; border-radius: 9999px; } .toggle-slider:before { position: absolute; content: ""; height: 0.9375rem; width: 0.9375rem; left: 0.15625rem; bottom: 0.15625rem; background-color: white; transition: all 0.2s ease-in-out; border-radius: 50%; box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1),0 1px 2px 0 rgba(0,0,0,0.06); }
        input:checked+.toggle-slider { background-color: #3B82F6; } input:checked+.toggle-slider:before { transform: translateX(1.25rem); }
        .settings-notification { position: fixed; bottom: 11rem; right: 1.5rem; z-index: 9999; padding: 0.5rem 0.75rem; background-color: #3B82F6; color: white; border-radius: 0.375rem; font-size: 0.8125rem; animation: slideInOut 3s ease-in-out; }
        @keyframes slideInOut { 0%, 100% { opacity: 0; transform: translateX(20px); } 15%, 85% { opacity: 1; transform: translateX(0); } }
      `,
    },
    _settingsDefinition: [], // 设置项定义数组
    /**
     * 初始化设置面板模块
     */
    init() {
      // 定义所有设置项
      this._settingsDefinition = [
        { key: this._config.KEYS.SCRIPT_ENABLED, label: 'ページ最適化', defaultValue: true, handler: this._handleScriptToggle.bind(this) },
        { key: this._config.KEYS.FURIGANA_VISIBLE, label: '振り仮名表示', defaultValue: true, handler: this._handleFuriganaToggle.bind(this) },
        { key: this._config.KEYS.IFRAME_LAZY_LOAD, label: '関連記事表示', defaultValue: true, handler: this._handleIframeStrategyToggle.bind(this) },
      ]
      // 注入样式、创建面板并初始化显示状态
      GM_addStyle(this._config.STYLES)
      this._createPanel()
      this._initializeFuriganaDisplay()
    },
    // 公共接口：检查脚本是否启用
    isScriptEnabled: () => GM_getValue(SettingsPanel._config.KEYS.SCRIPT_ENABLED, true),
    // 公共接口：iFrame LazyLoad 是否启用
    isLazyLoadEnabled: () => GM_getValue(SettingsPanel._config.KEYS.IFRAME_LAZY_LOAD, true),
    // 内部辅助函数：读取和保存设置
    _get: (k, d) => GM_getValue(k, d),
    _set: (k, v) => GM_setValue(k, v),
    // 内部辅助函数：显示通知
    _showNotification(message = '設定を保存しました。再読み込みしてください。') {
      const el = document.createElement('div')
      el.className = 'settings-notification'
      el.textContent = message
      document.body.appendChild(el)
      setTimeout(() => el.remove(), 2800) // 延长显示时间
    },
    // 事件处理器：处理主开关切换
    _handleScriptToggle(enabled) {
      this._set(this._config.KEYS.SCRIPT_ENABLED, enabled)
      this._showNotification()
    },
    // 事件处理器：处理振假名可见性切换
    _handleFuriganaToggle(visible) {
      this._set(this._config.KEYS.FURIGANA_VISIBLE, visible)
      // 此操作无需提示，因为是即时生效的
    },
    // 事件处理器：处理 iframe 加载策略切换
    _handleIframeStrategyToggle(enabled) {
      this._set(this._config.KEYS.IFRAME_LAZY_LOAD, enabled)
      this._showNotification()
    },
    /**
     * 切换振假名的 CSS `display` 属性
     * @param {boolean} visible - 是否可见
     */
    toggleFuriganaDisplay(visible) {
      const id = 'furigana-display-style'
      let style = document.getElementById(id)
      if (!style) {
        style = document.createElement('style')
        style.id = id
        document.head.appendChild(style)
      }
      style.textContent = `rt { display: ${visible ? 'ruby-text' : 'none'} !important; }`
    },
    // 内部辅助函数：根据保存的设置初始化振假名显示状态
    _initializeFuriganaDisplay() {
      if (!this._get(this._config.KEYS.FURIGANA_VISIBLE, true)) this.toggleFuriganaDisplay(false)
    },
    // 内部辅助函数：创建设置面板 DOM
    _createPanel() {
      const panel = document.createElement('div')
      panel.id = 'settings-panel'
      panel.innerHTML = `<h3 class="settings-title">設定パネル</h3>`
      this._settingsDefinition.forEach((s) => panel.appendChild(this._createToggle(s)))
      document.body.appendChild(panel)
    },
    // 内部辅助函数：为单个设置项创建开关 DOM
    _createToggle(config) {
      const id = `setting-${config.key}`
      const cont = document.createElement('div')
      cont.className = 'setting-item'
      cont.innerHTML = `<label for="${id}" class="setting-label">${config.label}</label><label class="toggle-switch"><input type="checkbox" id="${id}" ${this._get(config.key, config.defaultValue) ? 'checked' : ''}><span class="toggle-slider"></span></label>`
      cont.querySelector('input').addEventListener('change', (e) => config.handler(e.target.checked))
      return cont
    },
  }

  /**
   * @module MainController
   * @description 脚本主控制器，负责协调所有模块的初始化和执行流程
   * 它是整个脚本的“指挥官”，确保各个功能模块在正确的时机（`document-start` 或 `DOMContentLoaded`）
   * 以正确的顺序执行，从而实现脚本的全部功能
   */
  const MainController = {
    /**
     * 启动脚本的入口方法
     */
    run() {
      // 步骤 1: 检查脚本是否被用户禁用如果禁用，则仅加载设置面板，不执行任何页面修改
      if (!SettingsPanel.isScriptEnabled()) {
        document.addEventListener('DOMContentLoaded', () => SettingsPanel.init())
        return
      }
      // 步骤 2: 在 document-start 阶段，立即执行不依赖 DOM 内容的操作，以尽快生效
      PageOptimizer.init() // 注入样式，防止页面闪烁
      RubyConverter.init(RULES) // 预处理词库，为后续操作做准备

      // 步骤 3: 等待 DOM 完全加载后，执行依赖 DOM 内容的操作
      document.addEventListener('DOMContentLoaded', () => {
        IframeLazyLoader.init() // 初始化 iframe 加载器
        this._processPageContent() // 处理文章内容
        SettingsPanel.init() // 初始化设置面板 UI
      })
    },
    /**
     * 编排所有对页面主要内容的处理流程
     */
    _processPageContent() {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      if (articleBodies.length === 0) return

      let currentIndex = 0

      /**
       * 编排所有对页面主要内容的处理流程
       * 此方法采用异步分批处理（Asynchronous Batch Processing）的策略
       * 以避免在处理包含大量文章的长页面时，因脚本长时间占用主线程而导致的页面卡顿或无响应
       */
      const processBatch = () => {
        // 定义批次大小，每次处理 2 篇文章
        // Math.min 确保最后一批不会超出数组范围
        const batchSize = Math.min(2, articleBodies.length - currentIndex)
        const endIndex = currentIndex + batchSize

        // 在当前帧内，同步处理本批次的所有文章
        for (let i = currentIndex; i < endIndex; i++) {
          const body = articleBodies[i]
          IframeLazyLoader.processContainer(body)
          ImageProcessor.process(body)
          RubyConverter.processContainer(body)
          PageOptimizer.cleanupArticleBody(body) // 清理并显示
        }

        // 将索引移动到下一批次的起始位置
        currentIndex = endIndex

        // 检查是否还有未处理的文章
        if (currentIndex < articleBodies.length) {
          // 如果有，使用 requestAnimationFrame 请求浏览器在下一次重绘前调用 processBatch
          // 这能将处理任务分散到多个帧中，保持页面流畅
          requestAnimationFrame(processBatch)
        } else {
          // 所有批次处理完成，执行最终的全局布局调整
          PageOptimizer.finalizeLayout()
        }
      }

      // 启动第一个批次的处理
      requestAnimationFrame(processBatch)
    },
  }

  // 启动脚本
  MainController.run()
})()
