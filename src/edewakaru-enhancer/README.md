# 「絵でわかる日本語」閲覧体験強化

一个专门为[「絵でわかる日本語」](./src/edewakaru-enhancer/)网站设计的 Tampermonkey 用户脚本，通过智能注音转换和页面优化，显著提升日语学习的阅读体验。

## ✨ 功能特性

### 🔤 智能注音转换

- **括号注音转 Ruby 标签**：将 `漢字（かんじ）` 格式转换为标准的 HTML Ruby 标签显示
- **多优先级处理系统**：自动识别并处理各种注音格式
- **智能排除规则**：避免助词、纯假名等不适合转换的内容

### 🎨 页面布局优化

- **广告移除**：自动清理页面广告和无关元素
- **布局重构**：优化页面宽度和间距，提供更舒适的阅读环境
- **侧边栏优化**：保留分类导航，移除其他干扰元素
- **图片优化**：将缩略图链接转换为高质量原图显示

### ⚙️ 智能加载管理

- **iframe 延迟加载**：减少页面初始加载时间
- **点击加载模式**：可选的手动加载相关内容
- **批量处理**：异步分批处理文章，避免页面卡顿

### 🛠️ 用户设置面板

- **功能开关**：可独立控制脚本各项功能
- **振假名显示控制**：可随时切换注音的显示/隐藏
- **加载策略选择**：支持延迟加载和点击加载两种模式
- **设置持久化**：所有设置自动保存
